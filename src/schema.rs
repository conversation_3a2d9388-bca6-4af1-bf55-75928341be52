use std::fs::File;
use std::io::<PERSON><PERSON><PERSON>eader;
use struson::reader::<PERSON><PERSON>StreamReader;

use crate::types::{InferredJsonType, SchemaInferenceConfig};
use crate::paths::{VectorPath, VectorCapacities, PathComponent};
use crate::json::create_json_reader;

/// Calculate the total primitive capacity needed for an array type
fn calculate_primitive_capacity(element_type: &InferredJsonType, element_count: usize) -> usize {
    match element_type {
        InferredJsonType::Array { element_type: nested_element_type, .. } => {
            // For nested arrays, we need to calculate recursively
            // This is a simplified calculation - in practice, we'd need to scan the actual data
            // For now, assume each nested array has the same size as the first one
            element_count * calculate_primitive_capacity(nested_element_type.as_ref(), 1)
        },
        InferredJsonType::Object { .. } => {
            // Objects don't contribute to primitive capacity in arrays
            0
        },
        _ => {
            // Primitive types: each element contributes 1 to the capacity
            element_count
        }
    }
}

/// Root processing mode determines how the JSON file should be parsed and rows generated
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum RootProcessingMode {
    /// Root contains array of objects - each object becomes a row, array is flattened
    ArrayOfObjects,
    /// Root contains array of primitives - each primitive becomes a row, array is flattened
    ArrayOfPrimitives,
    /// Root contains array of arrays - each sub-array becomes a row, outer array is flattened
    ArrayOfArrays,
    /// Root contains single object - object becomes one row with struct columns
    SingleObject,
    /// Root contains single primitive - primitive becomes one row with single column
    SinglePrimitive,
}

/// Schema inference result with capacity information and processing mode
#[derive(Debug, Clone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
    pub capacities: VectorCapacities,
    pub processing_mode: RootProcessingMode,
}



/// Schema inference with capacity calculation
pub fn infer_schema_with_capacities(file_path: &str, config: SchemaInferenceConfig) -> Result<InferredSchema, Box<dyn std::error::Error>> {
    if config.enable_debug_output {
        eprintln!("SCHEMA INFERENCE: Starting schema analysis of file: {}", file_path);
    }

    // Open and parse the JSON file
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = create_json_reader(buf_reader);

    // Infer schema and calculate capacities in single pass
    let mut capacities = VectorCapacities::new();
    let root_type = infer_json_type_with_capacities(&mut json_reader, &config, &mut capacities, VectorPath::root())?;

    if config.enable_debug_output {
        eprintln!("SCHEMA INFERENCE: Detected root type: {:?}", root_type);
        eprintln!("SCHEMA INFERENCE: Calculated capacities: {:?}", capacities);
    }

    // Determine processing mode based on root type
    let processing_mode = determine_processing_mode(&root_type);

    let schema = InferredSchema {
        root_type,
        capacities,
        processing_mode,
    };

    Ok(schema)
}

/// Determine the processing mode based on the root JSON type
fn determine_processing_mode(root_type: &InferredJsonType) -> RootProcessingMode {
    match root_type {
        InferredJsonType::Array { element_type, .. } => {
            match element_type.as_ref() {
                InferredJsonType::Object { .. } => RootProcessingMode::ArrayOfObjects,
                InferredJsonType::Array { .. } => RootProcessingMode::ArrayOfArrays,
                _ => RootProcessingMode::ArrayOfPrimitives,
            }
        },
        InferredJsonType::Object { .. } => RootProcessingMode::SingleObject,
        _ => RootProcessingMode::SinglePrimitive,
    }
}

/// Legacy schema inference for backward compatibility
pub fn infer_schema_from_file(file_path: &str, config: SchemaInferenceConfig) -> Result<InferredSchema, Box<dyn std::error::Error>> {
    infer_schema_with_capacities(file_path, config)
}

/// Infer JSON type with capacity calculation
fn infer_json_type_with_capacities(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    config: &SchemaInferenceConfig,
    capacities: &mut VectorCapacities,
    current_path: VectorPath,
) -> Result<InferredJsonType, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Found array at path: {}", current_path.to_string());
            }

            json_reader.begin_array()?;

            if !json_reader.has_next()? {
                // Empty array
                json_reader.end_array()?;
                capacities.set_capacity(current_path, 0);
                return Ok(InferredJsonType::Array {
                    element_type: Box::new(InferredJsonType::Null),
                });
            }

            // Count elements and infer type from first element
            let mut element_count = 0;
            let mut element_path = current_path.clone();
            element_path.path_components.push(PathComponent::ArrayElement);

            // Infer type from first element
            let element_type = infer_json_type_with_capacities(json_reader, config, capacities, element_path.clone())?;
            element_count += 1;

            // Validate that all remaining elements have the same type
            while json_reader.has_next()? {
                let next_element_type = infer_json_type_with_capacities(json_reader, config, capacities, element_path.clone())?;

                // Check if the type matches the first element's type
                if !types_are_compatible(&element_type, &next_element_type) {
                    return Err(format!(
                        "Inconsistent array element types at path {}: expected {:?}, found {:?}",
                        current_path.to_string(),
                        element_type,
                        next_element_type
                    ).into());
                }

                element_count += 1;
            }

            json_reader.end_array()?;

            // Set capacity for this array (number of elements)
            capacities.set_capacity(current_path.clone(), element_count);

            // TODO: Calculate cumulative primitive capacity for nested arrays
            // This requires a more sophisticated approach that scans actual data
            // For now, skip this to avoid memory issues

            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Array has {} elements", element_count);
            }

            Ok(InferredJsonType::Array {
                element_type: Box::new(element_type),
            })
        },

        struson::reader::ValueType::Object => {
            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Found object at path: {}", current_path.to_string());
            }

            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let mut field_path = current_path.clone();
                field_path.path_components.push(PathComponent::ObjectField(field_name.clone()));
                
                let field_type = infer_json_type_with_capacities(json_reader, config, capacities, field_path)?;
                fields.push((field_name, field_type));

                if config.enable_debug_output {
                    eprintln!("SCHEMA INFERENCE: Found field: {}", fields.last().unwrap().0);
                }
            }

            json_reader.end_object()?;

            Ok(InferredJsonType::Object { fields })
        },

        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(InferredJsonType::String)
        },

        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(InferredJsonType::Number)
        },

        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(InferredJsonType::Boolean)
        },

        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(InferredJsonType::Null)
        },
    }
}

/// Check if two JSON types are compatible (same structure)
fn types_are_compatible(type1: &InferredJsonType, type2: &InferredJsonType) -> bool {
    match (type1, type2) {
        (InferredJsonType::String, InferredJsonType::String) => true,
        (InferredJsonType::Number, InferredJsonType::Number) => true,
        (InferredJsonType::Boolean, InferredJsonType::Boolean) => true,
        (InferredJsonType::Null, InferredJsonType::Null) => true,

        (InferredJsonType::Array { element_type: et1 }, InferredJsonType::Array { element_type: et2 }) => {
            types_are_compatible(et1, et2)
        },

        (InferredJsonType::Object { fields: f1 }, InferredJsonType::Object { fields: f2 }) => {
            // Objects are compatible if they have the same field names and compatible field types
            if f1.len() != f2.len() {
                return false;
            }

            // Simple comparison - for now, just check if field names and types match exactly
            for (field_name1, field_type1) in f1 {
                let mut found = false;
                for (field_name2, field_type2) in f2 {
                    if field_name1 == field_name2 {
                        if !types_are_compatible(field_type1, field_type2) {
                            return false;
                        }
                        found = true;
                        break;
                    }
                }
                if !found {
                    return false; // Field missing in second object
                }
            }

            true
        },

        _ => false, // Different types are not compatible
    }
}
