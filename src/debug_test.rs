#[cfg(test)]
mod debug_tests {
    use super::*;
    use crate::types::{TempJsonValue, InferredJsonType};
    use crate::stack_processor::StackBasedProcessor;
    use std::collections::HashMap;

    #[test]
    fn debug_2d_array_step_by_step() {
        // Create a simple 2D array: [[1, 2], [3, 4]]
        let inner_array_1 = TempJsonValue::Array(vec![
            TempJsonValue::Number(1.0),
            TempJsonValue::Number(2.0),
        ]);
        
        let inner_array_2 = TempJsonValue::Array(vec![
            TempJsonValue::Number(3.0),
            TempJsonValue::Number(4.0),
        ]);
        
        let root_array = TempJsonValue::Array(vec![inner_array_1, inner_array_2]);
        
        // Create the expected type
        let element_type = InferredJsonType::Array {
            element_type: Box::new(InferredJsonType::Number),
        };
        
        let array_type = InferredJsonType::Array {
            element_type: Box::new(element_type),
        };
        
        println!("=== DEBUG TEST START ===");
        println!("Root array: {:?}", root_array);
        println!("Expected type: {:?}", array_type);
        
        // Create a mock DataChunkHandle for testing
        // Note: This won't work without actual DuckDB integration
        // But we can at least test the stack processor logic
        
        let mut processor = StackBasedProcessor::new();
        
        // Test the count_primitive_elements function
        if let TempJsonValue::Array(elements) = &root_array {
            println!("=== TESTING count_primitive_elements ===");
            for (idx, element) in elements.iter().enumerate() {
                if let InferredJsonType::Array { element_type, .. } = &array_type {
                    let count = processor.count_primitive_elements(element, element_type.as_ref());
                    println!("Element {}: count = {}", idx, count);
                }
            }
        }
        
        // Test the offset calculation logic
        if let (TempJsonValue::Array(elements), InferredJsonType::Array { element_type, .. }) = (&root_array, &array_type) {
            println!("=== TESTING offset calculation ===");
            let mut cumulative_offset = 0;
            let mut element_offsets = Vec::new();
            
            for (idx, element) in elements.iter().enumerate() {
                element_offsets.push(cumulative_offset);
                let element_size = processor.count_primitive_elements(element, element_type.as_ref());
                println!("Element {}: offset = {}, size = {}", idx, cumulative_offset, element_size);
                cumulative_offset += element_size;
            }
            
            println!("Final element_offsets: {:?}", element_offsets);
            println!("Total cumulative_offset: {}", cumulative_offset);
        }
        
        println!("=== DEBUG TEST END ===");
    }
    
    #[test]
    fn debug_stack_processor_logic() {
        use crate::stack_processor::{WorkItem, VectorTarget};
        use crate::paths::ProjectionPath;
        
        println!("=== TESTING STACK PROCESSOR LOGIC ===");
        
        // Create test data
        let inner_array_1 = TempJsonValue::Array(vec![
            TempJsonValue::Number(1.0),
            TempJsonValue::Number(2.0),
        ]);
        
        let inner_array_2 = TempJsonValue::Array(vec![
            TempJsonValue::Number(3.0),
            TempJsonValue::Number(4.0),
        ]);
        
        let elements = vec![inner_array_1, inner_array_2];
        
        let element_type = InferredJsonType::Array {
            element_type: Box::new(InferredJsonType::Number),
        };
        
        // Test the offset calculation that would happen in process_array_items
        let mut processor = StackBasedProcessor::new();
        let mut cumulative_offset = 0;
        let mut element_offsets = Vec::new();
        
        // First pass: calculate cumulative offsets
        for (idx, element) in elements.iter().enumerate() {
            element_offsets.push(cumulative_offset);
            let element_size = processor.count_primitive_elements(element, &element_type);
            println!("Element {}: offset = {}, size = {}", idx, cumulative_offset, element_size);
            cumulative_offset += element_size;
        }
        
        // Test VectorTarget creation
        let target = VectorTarget::Column { col_idx: 0, row_idx: 0 };
        
        for (idx, element) in elements.iter().enumerate() {
            let element_target = VectorTarget::ListElement {
                parent: Box::new(target.clone()),
                element_idx: idx,
                cumulative_offset: element_offsets[idx],
            };
            
            println!("Created VectorTarget for element {}: {:?}", idx, element_target);
        }
        
        println!("=== STACK PROCESSOR LOGIC TEST END ===");
    }
}
