// Modular DuckDB JSON Extension
// Organized into logical modules for easier maintenance and refactoring

pub mod types;
pub mod paths;
pub mod json;
pub mod vectors;
pub mod stack_processor;
pub mod debug_test;
pub mod processing;
pub mod schema;
pub mod vtab;

// Re-export main public API
pub use schema::{InferredSchema, RootProcessingMode, infer_schema_with_capacities, infer_schema_from_file};
pub use types::{InferredJsonType, TempJsonValue, SchemaInferenceConfig};
pub use paths::{VectorPath, ProjectionPath, VectorCapacities};
pub use vtab::{JsonReaderVTab, streaming_json_reader_init};