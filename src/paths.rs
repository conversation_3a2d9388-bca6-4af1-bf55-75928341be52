use std::collections::HashMap;

/// Vector path identification for capacity management
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>h, Eq, PartialEq)]
pub struct VectorPath {
    pub path_components: Vec<PathComponent>,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON>h, Eq, PartialEq)]
pub enum PathComponent {
    Root,
    ArrayElement,
    ArrayChild,  // For child vectors of arrays
    ObjectField(String),
}

impl VectorPath {
    pub fn root() -> Self {
        Self { path_components: vec![PathComponent::Root] }
    }

    pub fn to_string(&self) -> String {
        self.path_components.iter()
            .map(|c| match c {
                PathComponent::Root => "root".to_string(),
                PathComponent::ArrayElement => "[]".to_string(),
                PathComponent::ArrayChild => "[child]".to_string(),
                PathComponent::ObjectField(name) => format!(".{}", name),
            })
            .collect::<Vec<_>>()
            .join("")
    }

    pub fn append(&self, component: PathComponent) -> Self {
        let mut new_components = self.path_components.clone();
        new_components.push(component);
        Self { path_components: new_components }
    }
}

/// Vector capacity management for two-pass processing
#[derive(Debug, Clone)]
pub struct VectorCapacities {
    pub capacities: HashMap<VectorPath, usize>,
    pub total_rows: usize,
}

impl VectorCapacities {
    pub fn new() -> Self {
        Self {
            capacities: HashMap::new(),
            total_rows: 0,
        }
    }

    pub fn set_capacity(&mut self, path: VectorPath, capacity: usize) {
        self.capacities.insert(path, capacity);
    }

    pub fn get_capacity(&self, path: &VectorPath) -> Option<usize> {
        self.capacities.get(path).copied()
    }

    pub fn set_total_rows(&mut self, rows: usize) {
        self.total_rows = rows;
    }
}

/// Represents a path for projection and vector access
#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct ProjectionPath {
    pub segments: Vec<PathSegment>,
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub enum PathSegment {
    Root,
    Field(String),           // obj.field
    ArrayIndex(usize),       // array[0]
}

impl ProjectionPath {
    pub fn root() -> Self {
        Self { segments: vec![PathSegment::Root] }
    }

    pub fn field(name: &str) -> Self {
        Self { segments: vec![PathSegment::Root, PathSegment::Field(name.to_string())] }
    }

    pub fn append(&self, segment: PathSegment) -> Self {
        let mut new_segments = self.segments.clone();
        new_segments.push(segment);
        Self { segments: new_segments }
    }
}

/// Convert ProjectionPath to VectorPath for capacity lookup
pub fn projection_path_to_vector_path(projection_path: &ProjectionPath) -> VectorPath {
    let mut components = Vec::new();
    
    for segment in &projection_path.segments {
        match segment {
            PathSegment::Root => components.push(PathComponent::Root),
            PathSegment::Field(name) => components.push(PathComponent::ObjectField(name.clone())),
            PathSegment::ArrayIndex(_) => components.push(PathComponent::ArrayElement),
        }
    }
    
    VectorPath { path_components: components }
}
