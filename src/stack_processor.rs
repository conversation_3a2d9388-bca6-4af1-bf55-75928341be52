use std::collections::HashMap;
use duckdb::core::{DataChunkHandle, Inserter};

use crate::types::{InferredJsonType, TempJsonValue};
use crate::paths::{VectorCapacities, ProjectionPath, PathSegment};

/// Stack-based JSON processor that handles arbitrary nesting without recursion
pub struct StackBasedProcessor {
    work_stack: Vec<WorkItem>,
    global_offset: usize, // Track global offset across all rows
}

/// A work item represents a piece of JSON data that needs to be processed
#[derive(Debug)]
enum WorkItem {
    /// Process a single JSON value into a specific vector location
    ProcessValue {
        value: TempJsonValue,
        expected_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
    },
    /// Process an array of values
    ProcessArray {
        elements: Vec<TempJsonValue>,
        element_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
    },
    /// Process an object's fields
    ProcessObject {
        fields: HashMap<String, TempJsonValue>,
        expected_fields: Vec<(String, InferredJsonType)>,
        target: VectorTarget,
        path: ProjectionPath,
    },
}

/// Specifies where to write the processed data with proper offset tracking
#[derive(Debug, Clone)]
enum VectorTarget {
    /// Write to a column in the main output
    Column {
        col_idx: usize,
        row_idx: usize
    },
    /// Write to a struct field
    StructField {
        parent: Box<VectorTarget>,
        field_idx: usize,
        row_idx: usize
    },
    /// Write to a list element with cumulative offset tracking
    ListElement {
        parent: Box<VectorTarget>,
        element_idx: usize,
        cumulative_offset: usize,  // Track cumulative offset for proper indexing
    },
}

impl StackBasedProcessor {
    pub fn new() -> Self {
        Self {
            work_stack: Vec::new(),
            global_offset: 0,
        }
    }

    /// Process a JSON value using stack-based iteration instead of recursion
    pub fn process_json_value(
        &mut self,
        value: TempJsonValue,
        expected_type: InferredJsonType,
        output: &DataChunkHandle,
        col_idx: usize,
        row_idx: usize,
        capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.process_json_value_with_offset(value, expected_type, output, col_idx, row_idx, capacities, 0)
    }

    pub fn process_json_value_with_offset(
        &mut self,
        value: TempJsonValue,
        expected_type: InferredJsonType,
        output: &DataChunkHandle,
        col_idx: usize,
        row_idx: usize,
        capacities: &VectorCapacities,
        global_offset: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        // Store the global offset for this processing session
        self.global_offset = global_offset;

        // Initialize the stack with the root work item
        let root_target = VectorTarget::Column { col_idx, row_idx };
        let root_path = ProjectionPath::root();

        self.work_stack.push(WorkItem::ProcessValue {
            value,
            expected_type,
            target: root_target,
            path: root_path,
        });

        // Process work items until stack is empty
        while let Some(work_item) = self.work_stack.pop() {
            self.process_work_item(work_item, output, capacities)?;
        }

        Ok(())
    }

    fn process_work_item(
        &mut self,
        work_item: WorkItem,
        output: &DataChunkHandle,
        capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        match work_item {
            WorkItem::ProcessValue { value, expected_type, target, path } => {
                self.process_single_value(value, expected_type, target, path, output, capacities)?;
            },
            WorkItem::ProcessArray { elements, element_type, target, path } => {
                self.process_array_items(elements, element_type, target, path, output, capacities)?;
            },
            WorkItem::ProcessObject { fields, expected_fields, target, path } => {
                self.process_object_fields(fields, expected_fields, target, path, output, capacities)?;
            },
        }

        Ok(())
    }

    fn process_single_value(
        &mut self,
        value: TempJsonValue,
        expected_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
        output: &DataChunkHandle,
        _capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {

        match (&value, &expected_type) {
            (TempJsonValue::Array(elements), InferredJsonType::Array { element_type, .. }) => {
                // Push array processing onto stack
                self.work_stack.push(WorkItem::ProcessArray {
                    elements: elements.clone(),
                    element_type: element_type.as_ref().clone(),
                    target,
                    path,
                });
            },
            (TempJsonValue::Object(fields), InferredJsonType::Object { fields: expected_fields, .. }) => {
                // Push object processing onto stack
                self.work_stack.push(WorkItem::ProcessObject {
                    fields: fields.clone(),
                    expected_fields: expected_fields.clone(),
                    target,
                    path,
                });
            },
            (TempJsonValue::Number(num), InferredJsonType::Number) => {
                self.write_primitive_to_target(target, output, PrimitiveValue::Number(*num))?;
            },
            (TempJsonValue::String(s), InferredJsonType::String) => {
                self.write_primitive_to_target(target, output, PrimitiveValue::String(s.clone()))?;
            },
            (TempJsonValue::Boolean(b), InferredJsonType::Boolean) => {
                self.write_primitive_to_target(target, output, PrimitiveValue::Boolean(*b))?;
            },
            (TempJsonValue::Null, _) => {
                self.write_null_to_target(target, output, &expected_type)?;
            },
            _ => {
                // Type mismatch - write null
                self.write_null_to_target(target, output, &expected_type)?;
            }
        }

        Ok(())
    }

    fn process_array_items(
        &mut self,
        elements: Vec<TempJsonValue>,
        element_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
        output: &DataChunkHandle,
        capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {

        // Set up the list vector for this array with proper cumulative offset tracking
        // We need to set up each list entry with the correct offset and length
        self.setup_list_vector_with_offsets(&target, output, &elements, &element_type, capacities)?;

        // Calculate cumulative offsets properly for nested arrays
        // We need to pre-calculate the cumulative offsets for all elements
        let mut cumulative_offset = 0;
        let mut element_offsets = Vec::new();

        // First pass: calculate cumulative offsets
        for element in &elements {
            element_offsets.push(cumulative_offset);

            // Calculate how many primitive elements this element contains
            let element_size = self.count_primitive_elements(element, &element_type);
            cumulative_offset += element_size;
        }

        // Push work items for each array element (in reverse order so they process in correct order)
        for (idx, element) in elements.into_iter().enumerate().rev() {
            let element_target = VectorTarget::ListElement {
                parent: Box::new(target.clone()),
                element_idx: idx,
                cumulative_offset: self.global_offset + element_offsets[idx], // Add global offset
            };

            let element_path = path.append(PathSegment::ArrayIndex(idx));

            self.work_stack.push(WorkItem::ProcessValue {
                value: element,
                expected_type: element_type.clone(),
                target: element_target,
                path: element_path,
            });
        }

        Ok(())
    }

    /// Count the number of primitive elements in a JSON value
    pub fn count_primitive_elements(&self, value: &TempJsonValue, expected_type: &InferredJsonType) -> usize {
        match (value, expected_type) {
            (TempJsonValue::Array(elements), InferredJsonType::Array { element_type, .. }) => {
                elements.iter()
                    .map(|elem| self.count_primitive_elements(elem, element_type.as_ref()))
                    .sum()
            },
            (TempJsonValue::Object(_), InferredJsonType::Object { .. }) => {
                // Objects don't contribute to primitive count in arrays
                0
            },
            _ => {
                // Primitive values count as 1
                1
            }
        }
    }

    fn process_object_fields(
        &mut self,
        fields: HashMap<String, TempJsonValue>,
        expected_fields: Vec<(String, InferredJsonType)>,
        target: VectorTarget,
        path: ProjectionPath,
        output: &DataChunkHandle,
        _capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        // Set up the struct vector for this object
        self.setup_struct_vector(&target, output, expected_fields.len())?;

        // Push work items for each field (in reverse order)
        for (field_idx, (field_name, field_type)) in expected_fields.into_iter().enumerate().rev() {
            let field_target = VectorTarget::StructField {
                parent: Box::new(target.clone()),
                field_idx,
                row_idx: self.get_target_row_index(&target),
            };
            
            let field_path = path.append(PathSegment::Field(field_name.clone()));
            
            if let Some(field_value) = fields.get(&field_name) {
                self.work_stack.push(WorkItem::ProcessValue {
                    value: field_value.clone(),
                    expected_type: field_type,
                    target: field_target,
                    path: field_path,
                });
            } else {
                // Field is missing - write null
                self.write_null_to_target(field_target, output, &field_type)?;
            }
        }

        Ok(())
    }

    // Helper methods for vector operations
    fn setup_list_vector_with_offsets(
        &self,
        target: &VectorTarget,
        output: &DataChunkHandle,
        elements: &[TempJsonValue],
        element_type: &InferredJsonType,
        _capacities: &VectorCapacities
    ) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                // For root-level arrays, we need to set up one entry per row
                // Each row corresponds to one element of the root array
                let mut list_vector = output.list_vector(*col_idx);

                // For a 2D array like [[1, 2], [3, 4]], we have:
                // - Row 0: points to [1, 2] (offset 0, length 2)
                // - Row 1: points to [3, 4] (offset 2, length 2)
                let mut cumulative_offset = 0;
                for (idx, element) in elements.iter().enumerate() {
                    let element_size = self.count_primitive_elements(element, element_type);
                    let row_index = *row_idx + idx;
                    list_vector.set_entry(row_index, cumulative_offset, element_size);
                    cumulative_offset += element_size;
                }
                Ok(())
            },
            VectorTarget::ListElement { parent, element_idx, cumulative_offset } => {
                // Handle nested list element arrays
                let list_vector = self.get_list_vector_for_target(parent.as_ref(), output)?;
                let mut nested_list_vector = list_vector.list_child();

                // For nested arrays, set up the entry for this specific element
                let element_size = elements.len(); // Number of primitive elements in this nested array
                nested_list_vector.set_entry(*element_idx, *cumulative_offset, element_size);
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for nested struct field arrays
                Err(format!("Nested struct field array setup not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
        }
    }

    fn setup_list_vector(&self, target: &VectorTarget, output: &DataChunkHandle, length: usize) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                let mut list_vector = output.list_vector(*col_idx);
                list_vector.set_entry(*row_idx, 0, length);
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for nested struct field arrays
                Err(format!("Nested struct field array setup not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent, element_idx, .. } => {
                // Handle nested list element arrays - recursively get the appropriate list vector
                let list_vector = self.get_list_vector_for_target(parent.as_ref(), output)?;
                let mut nested_list_vector = list_vector.list_child();
                nested_list_vector.set_entry(*element_idx, 0, length);
                Ok(())
            },
        }
    }

    fn setup_struct_vector(&self, target: &VectorTarget, output: &DataChunkHandle, _field_count: usize) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx: _ } => {
                // Struct vector setup for root column
                let _struct_vector = output.struct_vector(*col_idx);
                // The struct vector is already set up by DuckDB, we just need to populate it
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for nested struct field structs
                Err(format!("Nested struct field struct setup not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { element_idx, .. } => {
                // For now, raise error for nested list element structs
                Err(format!("Nested list element struct setup not yet implemented for element {}", element_idx).into())
            },
        }
    }

    fn write_primitive_to_target(&self, target: VectorTarget, output: &DataChunkHandle, value: PrimitiveValue) -> Result<(), Box<dyn std::error::Error>> {
        eprintln!("DEBUG STACK: write_primitive_to_target called with target: {:?}, value: {:?}", target, value);
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                match value {
                    PrimitiveValue::Number(num) => {
                        let mut flat_vector = output.flat_vector(col_idx);
                        let slice = flat_vector.as_mut_slice::<f64>();
                        if row_idx < slice.len() {
                            slice[row_idx] = num;
                        }
                    },
                    PrimitiveValue::String(s) => {
                        let flat_vector = output.flat_vector(col_idx);
                        flat_vector.insert(row_idx, s.as_str());
                    },
                    PrimitiveValue::Boolean(b) => {
                        let mut flat_vector = output.flat_vector(col_idx);
                        let slice = flat_vector.as_mut_slice::<bool>();
                        if row_idx < slice.len() {
                            slice[row_idx] = b;
                        }
                    },
                }
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for struct field primitives
                Err(format!("Struct field primitive writing not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent, element_idx: _, cumulative_offset } => {
                // Handle list element primitive writing - use cumulative offset for proper indexing
                let list_vector = self.get_list_vector_for_target(parent.as_ref(), output)?;

                // Use the pre-calculated capacity from schema inference
                // TODO: Get the actual capacity from VectorCapacities instead of hardcoding
                let total_capacity = 100; // This should come from capacities parameter
                let mut child_vector = list_vector.child(total_capacity);

                // Use cumulative_offset for proper positioning
                let actual_index = cumulative_offset;

                eprintln!("DEBUG STACK: Writing primitive at actual_index: {}, value: {:?}", actual_index, value);

                match value {
                    PrimitiveValue::Number(num) => {
                        let slice = child_vector.as_mut_slice::<f64>();
                        if actual_index < slice.len() {
                            slice[actual_index] = num;
                            eprintln!("DEBUG STACK: Successfully wrote {} at index {}", num, actual_index);
                        } else {
                            return Err(format!("Index {} out of bounds for number array of length {}", actual_index, slice.len()).into());
                        }
                    },
                    PrimitiveValue::String(s) => {
                        child_vector.insert(actual_index, s.as_str());
                        eprintln!("DEBUG STACK: Successfully wrote string '{}' at index {}", s, actual_index);
                    },
                    PrimitiveValue::Boolean(b) => {
                        let slice = child_vector.as_mut_slice::<bool>();
                        if actual_index < slice.len() {
                            slice[actual_index] = b;
                            eprintln!("DEBUG STACK: Successfully wrote {} at index {}", b, actual_index);
                        } else {
                            return Err(format!("Index {} out of bounds for boolean array of length {}", actual_index, slice.len()).into());
                        }
                    },
                }
                Ok(())
            },
        }
    }

    fn write_null_to_target(&self, target: VectorTarget, output: &DataChunkHandle, expected_type: &InferredJsonType) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                match expected_type {
                    InferredJsonType::Array { .. } => {
                        let mut list_vector = output.list_vector(col_idx);
                        list_vector.set_null(row_idx);
                    },
                    InferredJsonType::Object { .. } => {
                        let mut struct_vector = output.struct_vector(col_idx);
                        struct_vector.set_null(row_idx);
                    },
                    _ => {
                        let mut flat_vector = output.flat_vector(col_idx);
                        flat_vector.set_null(row_idx);
                    }
                }
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for struct field nulls
                Err(format!("Struct field null writing not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent, cumulative_offset, .. } => {
                // Handle list element null writing - use cumulative offset for proper indexing
                let list_vector = self.get_list_vector_for_target(parent.as_ref(), output)?;

                // Calculate the required capacity based on the cumulative offset
                let required_capacity = cumulative_offset + 1;
                let mut child_vector = list_vector.child(required_capacity);
                child_vector.set_null(cumulative_offset);
                Ok(())
            },
        }
    }

    fn get_target_row_index(&self, target: &VectorTarget) -> usize {
        match target {
            VectorTarget::Column { row_idx, .. } => *row_idx,
            VectorTarget::StructField { row_idx, .. } => *row_idx,
            VectorTarget::ListElement { element_idx, .. } => *element_idx,
        }
    }

    /// Recursively get the appropriate list vector for a target, handling arbitrary nesting depth
    fn get_list_vector_for_target(&self, target: &VectorTarget, output: &DataChunkHandle) -> Result<duckdb::core::ListVector, Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx: _ } => {
                // Base case: root column
                Ok(output.list_vector(*col_idx))
            },
            VectorTarget::ListElement { parent, .. } => {
                // Recursive case: get parent list vector and then get its list child
                let parent_list_vector = self.get_list_vector_for_target(parent.as_ref(), output)?;
                Ok(parent_list_vector.list_child())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For struct fields, we need to handle this differently
                Err(format!("Struct field list vector access not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{TempJsonValue, InferredJsonType};

    #[test]
    fn debug_offset_calculation() {
        println!("=== DEBUGGING OFFSET CALCULATION ===");

        // Create a simple 2D array: [[1, 2], [3, 4]]
        let inner_array_1 = TempJsonValue::Array(vec![
            TempJsonValue::Number(1.0),
            TempJsonValue::Number(2.0),
        ]);

        let inner_array_2 = TempJsonValue::Array(vec![
            TempJsonValue::Number(3.0),
            TempJsonValue::Number(4.0),
        ]);

        let elements = vec![inner_array_1, inner_array_2];

        // Create the expected type
        let element_type = InferredJsonType::Array {
            element_type: Box::new(InferredJsonType::Number),
        };

        println!("Elements structure:");
        for (i, element) in elements.iter().enumerate() {
            if let TempJsonValue::Array(inner) = element {
                println!("  Element {}: Array with {} items", i, inner.len());
                for (j, item) in inner.iter().enumerate() {
                    if let TempJsonValue::Number(n) = item {
                        println!("    [{}][{}] = {}", i, j, n);
                    }
                }
            }
        }

        // Test count_primitive_elements
        let processor = StackBasedProcessor::new();
        println!("\n=== TESTING count_primitive_elements ===");
        for (idx, element) in elements.iter().enumerate() {
            let count = processor.count_primitive_elements(element, &element_type);
            println!("Element {}: count = {}", idx, count);
        }

        // Test offset calculation (same logic as process_array_items)
        println!("\n=== TESTING offset calculation ===");
        let mut cumulative_offset = 0;
        let mut element_offsets = Vec::new();

        for (idx, element) in elements.iter().enumerate() {
            element_offsets.push(cumulative_offset);
            let element_size = processor.count_primitive_elements(element, &element_type);
            println!("Element {}: offset = {}, size = {}", idx, cumulative_offset, element_size);
            cumulative_offset += element_size;
        }

        println!("Final element_offsets: {:?}", element_offsets);
        println!("Total cumulative_offset: {}", cumulative_offset);

        // Verify expected values
        assert_eq!(element_offsets[0], 0, "First element should have offset 0");
        assert_eq!(element_offsets[1], 2, "Second element should have offset 2");
        assert_eq!(cumulative_offset, 4, "Total should be 4 primitive elements");

        println!("\n=== EXPECTED BEHAVIOR ===");
        println!("For [[1, 2], [3, 4]]:");
        println!("- Element 0 ([1, 2]) should write to offsets 0, 1");
        println!("- Element 1 ([3, 4]) should write to offsets 2, 3");
        println!("- Child vector should contain: [1.0, 2.0, 3.0, 4.0]");

        println!("=== DEBUG COMPLETE ===");
    }

    #[test]
    fn debug_vector_target_creation() {
        println!("=== DEBUGGING VectorTarget CREATION ===");

        // Create test data
        let elements = vec![
            TempJsonValue::Array(vec![
                TempJsonValue::Number(1.0),
                TempJsonValue::Number(2.0),
            ]),
            TempJsonValue::Array(vec![
                TempJsonValue::Number(3.0),
                TempJsonValue::Number(4.0),
            ]),
        ];

        let element_type = InferredJsonType::Array {
            element_type: Box::new(InferredJsonType::Number),
        };

        // Simulate the exact logic from process_array_items
        let processor = StackBasedProcessor::new();
        let mut cumulative_offset = 0;
        let mut element_offsets = Vec::new();

        // First pass: calculate cumulative offsets
        for (idx, element) in elements.iter().enumerate() {
            element_offsets.push(cumulative_offset);
            let element_size = processor.count_primitive_elements(element, &element_type);
            println!("Element {}: offset = {}, size = {}", idx, cumulative_offset, element_size);
            cumulative_offset += element_size;
        }

        // Create VectorTargets (same logic as process_array_items)
        let target = VectorTarget::Column { col_idx: 0, row_idx: 0 };

        for (idx, _element) in elements.iter().enumerate() {
            let element_target = VectorTarget::ListElement {
                parent: Box::new(target.clone()),
                element_idx: idx,
                cumulative_offset: element_offsets[idx],
            };

            println!("Created VectorTarget for element {}:", idx);
            match &element_target {
                VectorTarget::ListElement { parent: _, element_idx, cumulative_offset } => {
                    println!("  element_idx: {}, cumulative_offset: {}", element_idx, cumulative_offset);
                },
                _ => println!("  Unexpected target type"),
            }

            // This is what gets passed to write_primitive_to_target
            // Let's verify the cumulative_offset is preserved
            if let VectorTarget::ListElement { cumulative_offset, .. } = element_target {
                println!("  -> Cumulative offset for primitive writing: {}", cumulative_offset);

                // For element 0, this should be 0
                // For element 1, this should be 2
                if idx == 0 {
                    assert_eq!(cumulative_offset, 0, "Element 0 should have cumulative_offset 0");
                } else if idx == 1 {
                    assert_eq!(cumulative_offset, 2, "Element 1 should have cumulative_offset 2");
                }
            }
        }

        println!("=== VectorTarget creation test PASSED ===");
    }
}

#[derive(Debug)]
enum PrimitiveValue {
    Number(f64),
    String(String),
    Boolean(bool),
}
