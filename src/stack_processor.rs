use std::collections::HashMap;
use duckdb::core::{<PERSON><PERSON>hunk<PERSON><PERSON>le, Inserter};

use crate::types::{InferredJsonType, TempJsonValue};
use crate::paths::{VectorCapacities, ProjectionPath, PathSegment};

/// Stack-based JSON processor that handles arbitrary nesting without recursion
pub struct StackBasedProcessor {
    work_stack: Vec<WorkItem>,
}

/// A work item represents a piece of JSON data that needs to be processed
#[derive(Debug)]
enum WorkItem {
    /// Process a single JSON value into a specific vector location
    ProcessValue {
        value: TempJsonValue,
        expected_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
    },
    /// Process an array of values
    ProcessArray {
        elements: Vec<TempJsonValue>,
        element_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
    },
    /// Process an object's fields
    ProcessObject {
        fields: HashMap<String, TempJsonValue>,
        expected_fields: Vec<(String, InferredJsonType)>,
        target: VectorTarget,
        path: ProjectionPath,
    },
}

/// Specifies where to write the processed data
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
enum VectorTarget {
    /// Write to a column in the main output
    Column { col_idx: usize, row_idx: usize },
    /// Write to a struct field
    StructField { 
        parent: Box<VectorTarget>,
        field_idx: usize, 
        row_idx: usize 
    },
    /// Write to a list element
    ListElement { 
        parent: Box<VectorTarget>,
        element_idx: usize 
    },
}

impl StackBasedProcessor {
    pub fn new() -> Self {
        Self {
            work_stack: Vec::new(),
        }
    }

    /// Process a JSON value using stack-based iteration instead of recursion
    pub fn process_json_value(
        &mut self,
        value: TempJsonValue,
        expected_type: InferredJsonType,
        output: &DataChunkHandle,
        col_idx: usize,
        row_idx: usize,
        capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        // Initialize the stack with the root work item
        let root_target = VectorTarget::Column { col_idx, row_idx };
        let root_path = ProjectionPath::root();
        
        self.work_stack.push(WorkItem::ProcessValue {
            value,
            expected_type,
            target: root_target,
            path: root_path,
        });

        // Process work items until stack is empty
        while let Some(work_item) = self.work_stack.pop() {
            self.process_work_item(work_item, output, capacities)?;
        }

        Ok(())
    }

    fn process_work_item(
        &mut self,
        work_item: WorkItem,
        output: &DataChunkHandle,
        capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        match work_item {
            WorkItem::ProcessValue { value, expected_type, target, path } => {
                self.process_single_value(value, expected_type, target, path, output, capacities)?;
            },
            WorkItem::ProcessArray { elements, element_type, target, path } => {
                self.process_array_items(elements, element_type, target, path, output, capacities)?;
            },
            WorkItem::ProcessObject { fields, expected_fields, target, path } => {
                self.process_object_fields(fields, expected_fields, target, path, output, capacities)?;
            },
        }

        Ok(())
    }

    fn process_single_value(
        &mut self,
        value: TempJsonValue,
        expected_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
        output: &DataChunkHandle,
        _capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {

        match (&value, &expected_type) {
            (TempJsonValue::Array(elements), InferredJsonType::Array { element_type, .. }) => {
                // Push array processing onto stack
                self.work_stack.push(WorkItem::ProcessArray {
                    elements: elements.clone(),
                    element_type: element_type.as_ref().clone(),
                    target,
                    path,
                });
            },
            (TempJsonValue::Object(fields), InferredJsonType::Object { fields: expected_fields, .. }) => {
                // Push object processing onto stack
                self.work_stack.push(WorkItem::ProcessObject {
                    fields: fields.clone(),
                    expected_fields: expected_fields.clone(),
                    target,
                    path,
                });
            },
            (TempJsonValue::Number(num), InferredJsonType::Number) => {
                self.write_primitive_to_target(target, output, PrimitiveValue::Number(*num))?;
            },
            (TempJsonValue::String(s), InferredJsonType::String) => {
                self.write_primitive_to_target(target, output, PrimitiveValue::String(s.clone()))?;
            },
            (TempJsonValue::Boolean(b), InferredJsonType::Boolean) => {
                self.write_primitive_to_target(target, output, PrimitiveValue::Boolean(*b))?;
            },
            (TempJsonValue::Null, _) => {
                self.write_null_to_target(target, output, &expected_type)?;
            },
            _ => {
                // Type mismatch - write null
                self.write_null_to_target(target, output, &expected_type)?;
            }
        }

        Ok(())
    }

    fn process_array_items(
        &mut self,
        elements: Vec<TempJsonValue>,
        element_type: InferredJsonType,
        target: VectorTarget,
        path: ProjectionPath,
        output: &DataChunkHandle,
        _capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        // Set up the list vector for this array
        self.setup_list_vector(&target, output, elements.len())?;

        // Push work items for each array element (in reverse order so they process in correct order)
        for (idx, element) in elements.into_iter().enumerate().rev() {
            let element_target = VectorTarget::ListElement {
                parent: Box::new(target.clone()),
                element_idx: idx,
            };
            
            let element_path = path.append(PathSegment::ArrayIndex(idx));
            
            self.work_stack.push(WorkItem::ProcessValue {
                value: element,
                expected_type: element_type.clone(),
                target: element_target,
                path: element_path,
            });
        }

        Ok(())
    }

    fn process_object_fields(
        &mut self,
        fields: HashMap<String, TempJsonValue>,
        expected_fields: Vec<(String, InferredJsonType)>,
        target: VectorTarget,
        path: ProjectionPath,
        output: &DataChunkHandle,
        _capacities: &VectorCapacities,
    ) -> Result<(), Box<dyn std::error::Error>> {
        
        // Set up the struct vector for this object
        self.setup_struct_vector(&target, output, expected_fields.len())?;

        // Push work items for each field (in reverse order)
        for (field_idx, (field_name, field_type)) in expected_fields.into_iter().enumerate().rev() {
            let field_target = VectorTarget::StructField {
                parent: Box::new(target.clone()),
                field_idx,
                row_idx: self.get_target_row_index(&target),
            };
            
            let field_path = path.append(PathSegment::Field(field_name.clone()));
            
            if let Some(field_value) = fields.get(&field_name) {
                self.work_stack.push(WorkItem::ProcessValue {
                    value: field_value.clone(),
                    expected_type: field_type,
                    target: field_target,
                    path: field_path,
                });
            } else {
                // Field is missing - write null
                self.write_null_to_target(field_target, output, &field_type)?;
            }
        }

        Ok(())
    }

    // Helper methods for vector operations
    fn setup_list_vector(&self, target: &VectorTarget, output: &DataChunkHandle, length: usize) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                let mut list_vector = output.list_vector(*col_idx);
                list_vector.set_entry(*row_idx, 0, length);
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for nested struct field arrays
                Err(format!("Nested struct field array setup not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent, element_idx } => {
                // Handle nested list element arrays
                match parent.as_ref() {
                    VectorTarget::Column { col_idx, row_idx: _ } => {
                        // This is a nested array within a root-level array
                        let list_vector = output.list_vector(*col_idx);
                        let mut nested_list_vector = list_vector.list_child();
                        nested_list_vector.set_entry(*element_idx, 0, length);
                        Ok(())
                    },
                    _ => {
                        Err(format!("Deeply nested list element array setup not yet implemented for element {}", element_idx).into())
                    }
                }
            },
        }
    }

    fn setup_struct_vector(&self, target: &VectorTarget, output: &DataChunkHandle, _field_count: usize) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx: _ } => {
                // Struct vector setup for root column
                let _struct_vector = output.struct_vector(*col_idx);
                // The struct vector is already set up by DuckDB, we just need to populate it
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for nested struct field structs
                Err(format!("Nested struct field struct setup not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent: _, element_idx } => {
                // For now, raise error for nested list element structs
                Err(format!("Nested list element struct setup not yet implemented for element {}", element_idx).into())
            },
        }
    }

    fn write_primitive_to_target(&self, target: VectorTarget, output: &DataChunkHandle, value: PrimitiveValue) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                match value {
                    PrimitiveValue::Number(num) => {
                        let mut flat_vector = output.flat_vector(col_idx);
                        let slice = flat_vector.as_mut_slice::<f64>();
                        if row_idx < slice.len() {
                            slice[row_idx] = num;
                        }
                    },
                    PrimitiveValue::String(s) => {
                        let flat_vector = output.flat_vector(col_idx);
                        flat_vector.insert(row_idx, s.as_str());
                    },
                    PrimitiveValue::Boolean(b) => {
                        let mut flat_vector = output.flat_vector(col_idx);
                        let slice = flat_vector.as_mut_slice::<bool>();
                        if row_idx < slice.len() {
                            slice[row_idx] = b;
                        }
                    },
                }
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for struct field primitives
                Err(format!("Struct field primitive writing not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent, element_idx } => {
                // Handle list element primitive writing
                match parent.as_ref() {
                    VectorTarget::Column { col_idx, row_idx: _ } => {
                        // This is a primitive element in a root-level array
                        let list_vector = output.list_vector(*col_idx);
                        let total_capacity = 100; // TODO: Get from capacities
                        let mut child_vector = list_vector.child(total_capacity);

                        match value {
                            PrimitiveValue::Number(num) => {
                                let slice = child_vector.as_mut_slice::<f64>();
                                if element_idx < slice.len() {
                                    slice[element_idx] = num;
                                }
                            },
                            PrimitiveValue::String(s) => {
                                child_vector.insert(element_idx, s.as_str());
                            },
                            PrimitiveValue::Boolean(b) => {
                                let slice = child_vector.as_mut_slice::<bool>();
                                if element_idx < slice.len() {
                                    slice[element_idx] = b;
                                }
                            },
                        }
                        Ok(())
                    },
                    VectorTarget::ListElement { parent: grandparent, element_idx: parent_element_idx } => {
                        // This is a primitive element in a nested array
                        match grandparent.as_ref() {
                            VectorTarget::Column { col_idx, row_idx: _ } => {
                                // This is a 2D array: root -> list -> list -> primitive
                                let list_vector = output.list_vector(*col_idx);
                                let nested_list_vector = list_vector.list_child();
                                let total_capacity = 100; // TODO: Get from capacities
                                let mut child_vector = nested_list_vector.child(total_capacity);

                                match value {
                                    PrimitiveValue::Number(num) => {
                                        let slice = child_vector.as_mut_slice::<f64>();
                                        if element_idx < slice.len() {
                                            slice[element_idx] = num;
                                        }
                                    },
                                    PrimitiveValue::String(s) => {
                                        child_vector.insert(element_idx, s.as_str());
                                    },
                                    PrimitiveValue::Boolean(b) => {
                                        let slice = child_vector.as_mut_slice::<bool>();
                                        if element_idx < slice.len() {
                                            slice[element_idx] = b;
                                        }
                                    },
                                }
                                Ok(())
                            },
                            _ => {
                                Err(format!("Deeply nested list element primitive writing not yet implemented for element {}", element_idx).into())
                            }
                        }
                    },
                    _ => {
                        Err(format!("Other nested list element primitive writing not yet implemented for element {}", element_idx).into())
                    }
                }
            },
        }
    }

    fn write_null_to_target(&self, target: VectorTarget, output: &DataChunkHandle, expected_type: &InferredJsonType) -> Result<(), Box<dyn std::error::Error>> {
        match target {
            VectorTarget::Column { col_idx, row_idx } => {
                match expected_type {
                    InferredJsonType::Array { .. } => {
                        let mut list_vector = output.list_vector(col_idx);
                        list_vector.set_null(row_idx);
                    },
                    InferredJsonType::Object { .. } => {
                        let mut struct_vector = output.struct_vector(col_idx);
                        struct_vector.set_null(row_idx);
                    },
                    _ => {
                        let mut flat_vector = output.flat_vector(col_idx);
                        flat_vector.set_null(row_idx);
                    }
                }
                Ok(())
            },
            VectorTarget::StructField { parent: _, field_idx, row_idx } => {
                // For now, raise error for struct field nulls
                Err(format!("Struct field null writing not yet implemented for field {} row {}", field_idx, row_idx).into())
            },
            VectorTarget::ListElement { parent: _, element_idx } => {
                // For now, raise error for list element nulls
                Err(format!("List element null writing not yet implemented for element {}", element_idx).into())
            },
        }
    }

    fn get_target_row_index(&self, target: &VectorTarget) -> usize {
        match target {
            VectorTarget::Column { row_idx, .. } => *row_idx,
            VectorTarget::StructField { row_idx, .. } => *row_idx,
            VectorTarget::ListElement { parent: _, element_idx } => *element_idx,
        }
    }
}

#[derive(Debug)]
enum PrimitiveValue {
    Number(f64),
    String(String),
    Boolean(bool),
}
